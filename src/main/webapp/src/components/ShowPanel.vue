<!-- components/Shared/ShowPanel.vue -->
<template class="custom-drawer-width">
  <q-drawer side="right" width="25vw" show-if-above :model-value="open" behavior="desktop" overlay>
    <div class="bg-backgroundSecondary">
      <div class="show-panel-header">
        <div class="q-px-lg"></div>
      </div>
      <slot name="default" />
      <q-separator />
      <slot name="subsection" />
    </div>
  </q-drawer>
</template>

<script setup>
defineProps({
  open: <PERSON>olean,
  label: String,
  icon: String,
});
</script>
<style lang="scss">
.custom-drawer-width {
  width: 25vw !important;
}

.show-panel-header {
  padding: 12px 16px 24px;
  gap: 8px;
  display: grid;
}

.show-panel-label {
  p {
    margin: 0;
    font-weight: bolder;
  }
}
</style>
