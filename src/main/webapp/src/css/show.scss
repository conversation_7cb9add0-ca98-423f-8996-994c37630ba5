.q-page > .q-table__container {
  th:first-child:has(.q-checkbox), td:first-child:has(.q-checkbox) {
    display: none;
  }
}

.q-page > .q-drawer-container {
  overflow-wrap: anywhere;

  .q-drawer {
    width: 30vw !important;
    border-left: solid 1px $borderSecondary;
  }

  & > .q-card__section, .q-card__section--vert {
    padding: 12px 16px 24px;
  }

  h3 {
    line-height: normal;
  }

  .q-card__section {
    border-radius: 0 !important;

    .q-item__label:first-of-type {
      display: flex;
      align-items: center;
      gap: 4px;
      padding: 2px 4px;
      border-radius: 4px;
      width: fit-content;
      background-color: $infoLowest;
      color: $infoMedium;
    }

  }


  .q-card {
    border: none;
    border-radius: 0 !important;
    box-shadow: none !important;

    & > .q-card__section {
      background-color: $backgroundSecondary;
    }
  }

  .q-separator {
    margin: 0 16px;
  }

  .q-list {
    padding: 16px;

    .q-item, q-separator {
      padding: 0;
    }
  }

  .q-expansion-item .q-focus-helper {
    display: none;
  }


}
