<template>
  <q-drawer behavior="desktop" :model-value="true" side="right" show-if-above>
    <q-card class="backgroundSecondary" flat>
      {{ console.log(modelValue?.failures[0].line) }}
      <q-card-section class="col q-col-gutter-y-sm">
        <div class="row justify-end">
          <q-btn icon="close" @click="$emit('close')"></q-btn>
        </div>
        <q-item-section>
          <q-item-label>
            <q-icon name="file_copy" />
            <div>{{ modelValue?.type.replace(/_/g, ' ') }}</div>
          </q-item-label>
          <q-item-label>
            <h3>{{ modelValue?.name }}</h3>
          </q-item-label>
        </q-item-section>
        <!--      Download button -->
      </q-card-section>
      <q-list padding>
        <q-item>
          <q-item-section>Path</q-item-section>
          <q-item-section>{{ modelValue?.path }}</q-item-section>
        </q-item>
        <q-item>
          <q-item-section> Inserts</q-item-section>
          <q-item-section>{{ modelValue?.inserts }}</q-item-section>
        </q-item>
        <q-item>
          <q-item-section>Updates</q-item-section>
          <q-item-section>{{ modelValue?.updates }}</q-item-section>
        </q-item>
        <q-item>
          <q-item-section>Updates</q-item-section>
          <q-item-section>{{ modelValue?.deletes }}</q-item-section>
        </q-item>
      </q-list>

      <q-separator />

      <q-list>
        <q-expansion-item dense-toggle expand-separator default-opened label="Failures"></q-expansion-item>
      </q-list>
    </q-card>
  </q-drawer>
</template>

<script setup>
const props = defineProps({
  modelValue: Object,
});
</script>
